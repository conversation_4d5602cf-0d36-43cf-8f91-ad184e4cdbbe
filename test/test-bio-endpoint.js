// Simple test for bio analysis endpoint
// Run with: node test/test-bio-endpoint.js

async function testBioEndpoint() {
  console.log('🧪 Testing Bio Analysis Endpoint...');
  
  const testPayload = {
    bio: "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure and good conversation.",
    options: {
      includeRecommendations: true,
      includeImprovedBio: false,
      maxTokensPerStep: 800
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/bio...');
    console.log('📝 Bio:', testPayload.bio);
    
    const response = await fetch('http://localhost:8788/api/analyze/bio', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    console.log(`📥 Response headers:`, Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log(`📥 Raw response: ${responseText.substring(0, 500)}...`);
    
    if (!response.ok) {
      console.error('❌ Request failed with status:', response.status);
      console.error('❌ Response:', responseText);
      return;
    }

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError.message);
      console.error('❌ Raw response:', responseText);
      return;
    }
    
    if (result.success) {
      console.log('✅ Bio analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Steps Analyzed: ${result.data.steps.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Recommendations: ${result.data.recommendations?.length || 0}`);
      
      // Log step details
      if (result.data.steps && result.data.steps.length > 0) {
        console.log('📋 Step Details:');
        result.data.steps.forEach((step, index) => {
          console.log(`   Step ${step.stepId}: ${step.stepName} - Score: ${step.score}/100`);
          if (step.insights && step.insights.length > 0) {
            console.log(`     Insights: ${step.insights[0]}`);
          }
        });
      }
      
      // Log recommendations
      if (result.data.recommendations && result.data.recommendations.length > 0) {
        console.log('💡 Recommendations:');
        result.data.recommendations.forEach((rec, index) => {
          console.log(`   ${index + 1}. ${rec}`);
        });
      }
      
    } else {
      console.error('❌ Analysis failed:', result.error);
      console.error('❌ Error code:', result.code);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('❌ Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testBioEndpoint().catch(console.error);
}

module.exports = { testBioEndpoint };

#!/bin/bash

# Test runner for TinderOP API endpoints
# This script tests all the new server-side API endpoints

echo "🚀 TinderOP API Endpoint Test Suite"
echo "=================================="

# Check if the development server is running
echo "🔍 Checking if development server is running..."

# Test health endpoint first
if curl -s http://localhost:8788/api/health > /dev/null 2>&1; then
    echo "✅ Development server is running"
else
    echo "❌ Development server is not running"
    echo "Please start the development server with: bun run dev"
    exit 1
fi

echo ""
echo "🧪 Testing API Endpoints..."
echo ""

# Test 1: Basic Image Analysis
echo "1️⃣ Testing Basic Image Analysis (/api/analyze/image)"
echo "---------------------------------------------------"

curl -X POST http://localhost:8788/api/analyze/image \
  -H "Content-Type: application/json" \
  -d '{
    "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0+kgAAAABJRU5ErkJggg==",
    "fileName": "test.png",
    "options": {
      "includeRecommendations": true,
      "maxTokensPerStep": 500
    }
  }' \
  --max-time 60 \
  --silent \
  --show-error \
  | jq '.success, .data.overallScore, .data.steps | length' 2>/dev/null || echo "❌ Failed or invalid JSON response"

echo ""

# Test 2: Basic Bio Analysis
echo "2️⃣ Testing Basic Bio Analysis (/api/analyze/bio)"
echo "-----------------------------------------------"

curl -X POST http://localhost:8788/api/analyze/bio \
  -H "Content-Type: application/json" \
  -d '{
    "bio": "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure.",
    "options": {
      "includeRecommendations": true,
      "maxTokensPerStep": 500
    }
  }' \
  --max-time 60 \
  --silent \
  --show-error \
  | jq '.success, .data.overallScore, .data.steps | length' 2>/dev/null || echo "❌ Failed or invalid JSON response"

echo ""

# Test 3: Advanced Image Analysis
echo "3️⃣ Testing Advanced Image Analysis (/api/analyze/image-pro)"
echo "---------------------------------------------------------"

curl -X POST http://localhost:8788/api/analyze/image-pro \
  -H "Content-Type: application/json" \
  -d '{
    "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0+kgAAAABJRU5ErkJggg==",
    "fileName": "test-pro.png",
    "options": {
      "expertPersonas": ["dating_psychology_expert", "fashion_stylist"],
      "includeConfidenceAnalysis": true,
      "maxTokensPerExpert": 1000
    }
  }' \
  --max-time 120 \
  --silent \
  --show-error \
  | jq '.success, .data.overallScore, .data.expertAnalyses | length' 2>/dev/null || echo "❌ Failed or invalid JSON response"

echo ""

# Test 4: Advanced Bio Analysis
echo "4️⃣ Testing Advanced Bio Analysis (/api/analyze/bio-pro)"
echo "-----------------------------------------------------"

curl -X POST http://localhost:8788/api/analyze/bio-pro \
  -H "Content-Type: application/json" \
  -d '{
    "bio": "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure.",
    "options": {
      "expertPersonas": ["dating_psychology_expert", "copywriting_specialist"],
      "includeConfidenceAnalysis": true,
      "includeImprovedBio": true,
      "tone": "sincere",
      "maxTokensPerExpert": 1000
    }
  }' \
  --max-time 120 \
  --silent \
  --show-error \
  | jq '.success, .data.overallScore, .data.expertAnalyses | length' 2>/dev/null || echo "❌ Failed or invalid JSON response"

echo ""

# Test 5: CORS Preflight
echo "5️⃣ Testing CORS Preflight (OPTIONS requests)"
echo "-------------------------------------------"

for endpoint in "image" "bio" "image-pro" "bio-pro"; do
    echo "Testing OPTIONS /api/analyze/$endpoint"
    
    response=$(curl -X OPTIONS http://localhost:8788/api/analyze/$endpoint \
      -H "Origin: http://localhost:3000" \
      -H "Access-Control-Request-Method: POST" \
      -H "Access-Control-Request-Headers: Content-Type" \
      --silent \
      --write-out "HTTP_CODE:%{http_code}")
    
    http_code=$(echo "$response" | grep -o "HTTP_CODE:[0-9]*" | cut -d: -f2)
    
    if [ "$http_code" = "200" ]; then
        echo "✅ CORS preflight successful for $endpoint"
    else
        echo "❌ CORS preflight failed for $endpoint (HTTP $http_code)"
    fi
done

echo ""

# Test 6: Error Handling
echo "6️⃣ Testing Error Handling"
echo "------------------------"

echo "Testing invalid image data..."
curl -X POST http://localhost:8788/api/analyze/image \
  -H "Content-Type: application/json" \
  -d '{
    "image": "invalid-image-data",
    "fileName": "test.png"
  }' \
  --silent \
  | jq '.success, .error' 2>/dev/null || echo "❌ Failed to get error response"

echo ""

echo "Testing missing bio data..."
curl -X POST http://localhost:8788/api/analyze/bio \
  -H "Content-Type: application/json" \
  -d '{}' \
  --silent \
  | jq '.success, .error' 2>/dev/null || echo "❌ Failed to get error response"

echo ""
echo "🎉 Test suite completed!"
echo ""
echo "📝 Notes:"
echo "- If you see 'null' responses, the endpoints might not be working correctly"
echo "- If you see 'Failed or invalid JSON response', there might be server errors"
echo "- Check the development server logs for detailed error information"
echo "- Make sure OPENROUTER_API_KEY is set in your environment"

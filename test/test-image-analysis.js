// Test script for image analysis endpoint
// Run with: node test/test-image-analysis.js

const fs = require('fs');
const path = require('path');

// Create a simple test image (1x1 pixel PNG in base64)
const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0+kgAAAABJRU5ErkJggg==';

async function testImageAnalysis() {
  console.log('🧪 Testing Image Analysis Endpoint...');
  
  const testPayload = {
    image: `data:image/png;base64,${testImageBase64}`,
    fileName: 'test-image.png',
    options: {
      includeRecommendations: true,
      maxTokensPerStep: 500
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/image...');
    
    const response = await fetch('http://localhost:8788/api/analyze/image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Image analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Steps Analyzed: ${result.data.steps.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Recommendations: ${result.data.recommendations?.length || 0}`);
      
      // Log step details
      result.data.steps.forEach((step, index) => {
        console.log(`   Step ${step.stepId}: ${step.stepName} - Score: ${step.score}/100`);
      });
      
    } else {
      console.error('❌ Analysis failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testBioAnalysis() {
  console.log('\n🧪 Testing Bio Analysis Endpoint...');
  
  const testPayload = {
    bio: "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure and good conversation.",
    options: {
      includeRecommendations: true,
      includeImprovedBio: false,
      maxTokensPerStep: 500
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/bio...');
    
    const response = await fetch('http://localhost:8788/api/analyze/bio', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Bio analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Steps Analyzed: ${result.data.steps.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Recommendations: ${result.data.recommendations?.length || 0}`);
      
      // Log step details
      result.data.steps.forEach((step, index) => {
        console.log(`   Step ${step.stepId}: ${step.stepName} - Score: ${step.score}/100`);
      });
      
    } else {
      console.error('❌ Analysis failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testAdvancedImageAnalysis() {
  console.log('\n🧪 Testing Advanced Image Analysis Endpoint...');
  
  const testPayload = {
    image: `data:image/png;base64,${testImageBase64}`,
    fileName: 'test-image-pro.png',
    options: {
      expertPersonas: ['dating_psychology_expert', 'fashion_stylist'],
      includeConfidenceAnalysis: true,
      includeRecommendations: true,
      maxTokensPerExpert: 1000
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/image-pro...');
    
    const response = await fetch('http://localhost:8788/api/analyze/image-pro', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Advanced image analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Expert Analyses: ${result.data.expertAnalyses.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Confidence: ${result.data.confidenceAnalysis.overallConfidence}/100`);
      
      // Log expert details
      result.data.expertAnalyses.forEach((expert, index) => {
        console.log(`   Expert ${expert.expertType}: ${expert.expertName} - Score: ${expert.score}/100`);
      });
      
    } else {
      console.error('❌ Analysis failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testAdvancedBioAnalysis() {
  console.log('\n🧪 Testing Advanced Bio Analysis Endpoint...');
  
  const testPayload = {
    bio: "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure and good conversation.",
    options: {
      expertPersonas: ['dating_psychology_expert', 'copywriting_specialist'],
      includeConfidenceAnalysis: true,
      includeImprovedBio: true,
      tone: 'sincere',
      maxTokensPerExpert: 1000
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/bio-pro...');
    
    const response = await fetch('http://localhost:8788/api/analyze/bio-pro', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Advanced bio analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Expert Analyses: ${result.data.expertAnalyses.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Confidence: ${result.data.confidenceAnalysis.overallConfidence}/100`);
      console.log(`   - Improved Bio: ${result.data.improvedBio ? 'Generated' : 'Not generated'}`);
      
      // Log expert details
      result.data.expertAnalyses.forEach((expert, index) => {
        console.log(`   Expert ${expert.expertType}: ${expert.expertName} - Score: ${expert.score}/100`);
      });
      
    } else {
      console.error('❌ Analysis failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting API Endpoint Tests...\n');
  
  await testImageAnalysis();
  await testBioAnalysis();
  await testAdvancedImageAnalysis();
  await testAdvancedBioAnalysis();
  
  console.log('\n✅ All tests completed!');
}

// Check if we're running this script directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testImageAnalysis,
  testBioAnalysis,
  testAdvancedImageAnalysis,
  testAdvancedBioAnalysis,
  runAllTests
};

// Test script for advanced analysis endpoints
// Run with: node test/test-advanced-endpoints.js

// Create a simple test image (1x1 pixel PNG in base64)
const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0+kgAAAABJRU5ErkJggg==';

async function testAdvancedImageAnalysis() {
  console.log('🧪 Testing Advanced Image Analysis Endpoint...');
  
  const testPayload = {
    image: `data:image/png;base64,${testImageBase64}`,
    fileName: 'test-image-pro.png',
    options: {
      expertPersonas: ['dating_psychology_expert', 'fashion_stylist'],
      includeConfidenceAnalysis: true,
      includeRecommendations: true,
      maxTokensPerExpert: 1500
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/image-pro...');
    console.log('📝 Expert personas:', testPayload.options.expertPersonas);
    
    const response = await fetch('http://localhost:8788/api/analyze/image-pro', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    const responseText = await response.text();
    console.log(`📥 Raw response length: ${responseText.length} characters`);
    
    if (!response.ok) {
      console.error('❌ Request failed with status:', response.status);
      console.error('❌ Response:', responseText.substring(0, 1000));
      return;
    }

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError.message);
      console.error('❌ Raw response:', responseText.substring(0, 1000));
      return;
    }
    
    if (result.success) {
      console.log('✅ Advanced image analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Analysis ID: ${result.data.analysisId}`);
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Expert Analyses: ${result.data.expertAnalyses.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Confidence: ${result.data.confidenceAnalysis.overallConfidence}/100`);
      console.log(`   - Expert Agreement: ${result.data.confidenceAnalysis.expertAgreement}/100`);
      
      // Log expert details
      if (result.data.expertAnalyses && result.data.expertAnalyses.length > 0) {
        console.log('👥 Expert Analysis Details:');
        result.data.expertAnalyses.forEach((expert, index) => {
          console.log(`   Expert ${index + 1}: ${expert.expertName} (${expert.expertType})`);
          console.log(`     Score: ${expert.score}/100`);
          console.log(`     Confidence: ${expert.confidence}/100`);
          console.log(`     Processing Time: ${expert.processingTime}ms`);
          if (expert.insights && expert.insights.length > 0) {
            console.log(`     Key Insight: ${expert.insights[0]}`);
          }
          if (expert.recommendations && expert.recommendations.length > 0) {
            console.log(`     Top Recommendation: ${expert.recommendations[0]}`);
          }
        });
      }
      
      // Log consolidated insights
      if (result.data.consolidatedInsights && result.data.consolidatedInsights.length > 0) {
        console.log('💡 Consolidated Insights:');
        result.data.consolidatedInsights.slice(0, 3).forEach((insight, index) => {
          console.log(`   ${index + 1}. ${insight}`);
        });
      }
      
    } else {
      console.error('❌ Analysis failed:', result.error);
      console.error('❌ Error code:', result.code);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('❌ Stack trace:', error.stack);
  }
}

async function testAdvancedBioAnalysis() {
  console.log('\n🧪 Testing Advanced Bio Analysis Endpoint...');
  
  const testPayload = {
    bio: "I love hiking, reading, and trying new restaurants. Looking for someone who shares my passion for adventure and good conversation. I work in tech and enjoy both outdoor activities and cozy nights in.",
    options: {
      expertPersonas: ['dating_psychology_expert', 'copywriting_specialist', 'relationship_coach'],
      includeConfidenceAnalysis: true,
      includeImprovedBio: true,
      tone: 'sincere',
      maxTokensPerExpert: 1500
    }
  };

  try {
    console.log('📤 Sending request to /api/analyze/bio-pro...');
    console.log('📝 Bio:', testPayload.bio);
    console.log('📝 Expert personas:', testPayload.options.expertPersonas);
    console.log('📝 Tone:', testPayload.options.tone);
    
    const response = await fetch('http://localhost:8788/api/analyze/bio-pro', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    const responseText = await response.text();
    console.log(`📥 Raw response length: ${responseText.length} characters`);
    
    if (!response.ok) {
      console.error('❌ Request failed with status:', response.status);
      console.error('❌ Response:', responseText.substring(0, 1000));
      return;
    }

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError.message);
      console.error('❌ Raw response:', responseText.substring(0, 1000));
      return;
    }
    
    if (result.success) {
      console.log('✅ Advanced bio analysis successful!');
      console.log('📊 Results:');
      console.log(`   - Analysis ID: ${result.data.analysisId}`);
      console.log(`   - Overall Score: ${result.data.overallScore}/100`);
      console.log(`   - Expert Analyses: ${result.data.expertAnalyses.length}`);
      console.log(`   - Processing Time: ${result.data.processingTime}ms`);
      console.log(`   - Confidence: ${result.data.confidenceAnalysis.overallConfidence}/100`);
      console.log(`   - Expert Agreement: ${result.data.confidenceAnalysis.expertAgreement}/100`);
      console.log(`   - Improved Bio Generated: ${result.data.improvedBio ? 'Yes' : 'No'}`);
      
      // Log expert details
      if (result.data.expertAnalyses && result.data.expertAnalyses.length > 0) {
        console.log('👥 Expert Analysis Details:');
        result.data.expertAnalyses.forEach((expert, index) => {
          console.log(`   Expert ${index + 1}: ${expert.expertName} (${expert.expertType})`);
          console.log(`     Score: ${expert.score}/100`);
          console.log(`     Confidence: ${expert.confidence}/100`);
          console.log(`     Processing Time: ${expert.processingTime}ms`);
          if (expert.insights && expert.insights.length > 0) {
            console.log(`     Key Insight: ${expert.insights[0]}`);
          }
          if (expert.recommendations && expert.recommendations.length > 0) {
            console.log(`     Top Recommendation: ${expert.recommendations[0]}`);
          }
        });
      }
      
      // Log consolidated insights
      if (result.data.consolidatedInsights && result.data.consolidatedInsights.length > 0) {
        console.log('💡 Consolidated Insights:');
        result.data.consolidatedInsights.slice(0, 3).forEach((insight, index) => {
          console.log(`   ${index + 1}. ${insight}`);
        });
      }
      
      // Log improved bio
      if (result.data.improvedBio) {
        console.log('✨ Improved Bio:');
        console.log(`   "${result.data.improvedBio}"`);
      }
      
    } else {
      console.error('❌ Analysis failed:', result.error);
      console.error('❌ Error code:', result.code);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('❌ Stack trace:', error.stack);
  }
}

// Run all tests
async function runAdvancedTests() {
  console.log('🚀 Starting Advanced Analysis Tests...\n');
  
  await testAdvancedImageAnalysis();
  await testAdvancedBioAnalysis();
  
  console.log('\n✅ Advanced analysis tests completed!');
}

// Check if we're running this script directly
if (require.main === module) {
  runAdvancedTests().catch(console.error);
}

module.exports = {
  testAdvancedImageAnalysis,
  testAdvancedBioAnalysis,
  runAdvancedTests
};

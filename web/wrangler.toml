name = "tinderop"
compatibility_date = "2025-04-03"
compatibility_flags = ["nodejs_compat"]

[assets]
not_found_handling = "single-page-application"

[build]
command = "bun run build"

[vars]
NODE_ENV = "production"

# Environment-specific configurations
[env.development.vars]
NODE_ENV = "development"
# OPENROUTER_API_KEY = "your-dev-key-here"  # Set via wrangler secret put

[env.production.vars]
NODE_ENV = "production"
# OPENROUTER_API_KEY = "your-prod-key-here"  # Set via wrangler secret put
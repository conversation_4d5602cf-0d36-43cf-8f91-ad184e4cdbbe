import type { PagesFunction } from "@cloudflare/workers-types";
import type { Env } from "../../../types/env";
import { 
  OpenRouterClient, 
  MODELS, 
  createSuccessResponse, 
  createErrorResponse, 
  handleCORS,
  validateImageInput,
  ValidationError,
  OpenRouterError
} from "../../_shared/openrouter-client";

// Analysis steps configuration (matches client-side ANALYSIS_STEPS)
const ANALYSIS_STEPS = [
  { id: 1, name: "Technical Quality", description: "Analyzing photo quality, lighting, and composition" },
  { id: 2, name: "Facial Analysis", description: "Evaluating facial features, expressions, and attractiveness" },
  { id: 3, name: "Physical Analysis", description: "Assessing body language, posture, and physical presentation" },
  { id: 4, name: "Style & Presentation", description: "Assessing clothing, setting, and overall presentation" },
  { id: 5, name: "Dating Profile Optimization", description: "Analyzing dating-specific appeal and suitability" },
  { id: 6, name: "Final Recommendations", description: "Synthesizing insights into actionable advice" }
] as const;

interface ImageAnalysisRequest {
  image: string; // base64 encoded image with data URL prefix
  fileName?: string;
  options?: {
    includeRecommendations?: boolean;
    maxTokensPerStep?: number;
  };
}

interface StepResult {
  stepId: number;
  stepName: string;
  score: number;
  insights: string[];
  confidence: number;
  processingTime: number;
}

interface ImageAnalysisResponse {
  steps: StepResult[];
  overallScore: number;
  recommendations: string[];
  fileName?: string;
  processingTime: number;
}

// Handle CORS preflight requests
export const onRequestOptions: PagesFunction = async () => {
  return handleCORS();
};

// Main POST handler for image analysis
export const onRequestPost: PagesFunction<Env> = async (context) => {
  const startTime = Date.now();
  
  try {
    console.log('🔍 TinderOP: Received image analysis request');
    
    // Parse request body
    const body = await context.request.json() as ImageAnalysisRequest;
    
    // Validate request
    if (!body.image) {
      return createErrorResponse('Image data is required', 400, Date.now() - startTime);
    }
    
    validateImageInput(body.image);
    
    // Initialize OpenRouter client
    const client = new OpenRouterClient(context.env);
    
    // Extract base64 data from data URL
    const base64Data = body.image.replace(/^data:image\/[a-z]+;base64,/, '');
    
    console.log(`📸 Processing image analysis for: ${body.fileName || 'unnamed'}`);
    console.log(`🔧 Analysis steps: ${ANALYSIS_STEPS.length}`);
    
    // Execute analysis steps
    const results: StepResult[] = [];
    
    for (let i = 0; i < ANALYSIS_STEPS.length; i++) {
      const step = ANALYSIS_STEPS[i];
      const stepStartTime = Date.now();
      
      try {
        console.log(`🔍 Executing step ${step.id}: ${step.name}`);
        
        const prompt = getStepPrompt(step.id);
        const maxTokens = body.options?.maxTokensPerStep || 1000;
        
        const result = await client.generateText({
          model: MODELS.BASIC,
          messages: [
            {
              role: "user",
              content: [
                { type: "text", text: prompt },
                { type: "image", image: `data:image/jpeg;base64,${base64Data}` }
              ]
            }
          ],
          maxTokens,
          temperature: 0.3
        });
        
        const parsedResult = parseAnalysisResult(result.text);
        const processingTime = Date.now() - stepStartTime;
        
        console.log(`✅ Step ${step.id} completed: score=${parsedResult.score}, confidence=${parsedResult.confidence}, time=${processingTime}ms`);
        
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: parsedResult.score,
          insights: parsedResult.insights,
          confidence: parsedResult.confidence,
          processingTime
        });
        
      } catch (error) {
        console.error(`❌ Error in step ${step.id}:`, error);
        
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ["Analysis failed for this step. Please try again."],
          confidence: 0,
          processingTime: Date.now() - stepStartTime
        });
      }
    }
    
    // Calculate overall score
    const overallScore = calculateOverallScore(results);
    
    // Generate recommendations if requested
    const recommendations = body.options?.includeRecommendations !== false 
      ? generateFinalRecommendations(results)
      : [];
    
    const totalProcessingTime = Date.now() - startTime;
    
    console.log(`📊 Image analysis completed: overall score=${overallScore}, time=${totalProcessingTime}ms`);
    
    const response: ImageAnalysisResponse = {
      steps: results,
      overallScore,
      recommendations,
      fileName: body.fileName,
      processingTime: totalProcessingTime
    };
    
    return createSuccessResponse(response, totalProcessingTime);
    
  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    if (error instanceof ValidationError) {
      return createErrorResponse(error.message, 400, processingTime, "VALIDATION_ERROR");
    }
    
    if (error instanceof OpenRouterError) {
      return createErrorResponse(error.message, error.statusCode, processingTime, error.code);
    }
    
    console.error('❌ Unexpected error in image analysis:', error);
    return createErrorResponse(
      'Internal server error during image analysis', 
      500, 
      processingTime, 
      "INTERNAL_ERROR"
    );
  }
};

function getStepPrompt(stepId: number): string {
  const baseInstruction = `You are a BRUTALLY HONEST dating profile photo expert. Most photos are mediocre and deserve low scores. Be ruthlessly critical and objective.

SCORING PHILOSOPHY:
- 90-100: EXCEPTIONAL - Top 5% of all dating photos (near-perfect)
- 80-89: EXCELLENT - Top 15% (very strong with minor flaws)
- 70-79: GOOD - Above average but notable issues
- 60-69: AVERAGE - Typical photo, significant improvement needed
- 50-59: BELOW AVERAGE - Multiple issues, major work needed
- 40-49: POOR - Serious problems, likely to perform badly
- 30-39: VERY POOR - Major red flags, repels matches
- 20-29: TERRIBLE - Fundamentally broken
- 10-19: AWFUL - Actively harmful to dating prospects
- 0-9: CATASTROPHIC - Should not be used

Provide a JSON response with exactly this structure:
{
  "score": <number 0-100>,
  "insights": ["harsh_truth1", "critical_flaw2", "brutal_feedback3"],
  "confidence": <number 0-100>
}

Be BRUTALLY HONEST. Most photos deserve 30-60 scores. Only exceptional photos get 80+.`;

  const stepPrompts: Record<number, string> = {
    1: `${baseInstruction}

STEP 1: TECHNICAL QUALITY ASSESSMENT - BE RUTHLESSLY CRITICAL
Most photos have terrible technical quality. Be harsh about every flaw:

CRITICAL ASSESSMENT AREAS:
- Image resolution and sharpness (most are blurry/pixelated)
- Lighting quality (harsh shadows, poor exposure, unflattering angles)
- Composition and framing (off-center, poor cropping, amateur mistakes)
- Background quality (cluttered, distracting, unprofessional)
- Color balance and saturation (oversaturated, poor white balance)
- Overall photo clarity (most are amateur selfies)

HARSH REALITY: Most dating photos are low-quality selfies that hurt rather than help. Be brutal about technical flaws.`,

    2: `${baseInstruction}

STEP 2: FACIAL ANALYSIS - BRUTAL ATTRACTIVENESS ASSESSMENT
Be ruthlessly honest about facial appeal and dating market reality:

CRITICAL FACIAL ASSESSMENT:
- Facial symmetry and proportions (most faces have significant asymmetries)
- Expression quality (forced smiles, awkward expressions, unflattering angles)
- Eye contact and gaze (dead eyes, looking away, lack of confidence)
- Skin quality and grooming (blemishes, poor grooming, unflattering lighting)
- Facial hair and styling (unkempt, poorly maintained, unflattering styles)
- Overall facial attractiveness (be honest about dating market appeal)

HARSH REALITY: Facial attractiveness heavily impacts dating success. Be brutally honest about market appeal.`,

    3: `${baseInstruction}

STEP 3: PHYSICAL ANALYSIS & BODY LANGUAGE - BRUTAL ASSESSMENT
Most people have poor body language and physical presentation. Be ruthlessly critical:

CRITICAL PHYSICAL ASSESSMENT:
- Posture and body positioning (slouching, awkward poses, poor confidence)
- Body language and confidence signals (insecurity, awkwardness, poor presence)
- Physical fitness and body composition (be honest about fitness level)
- Height and proportions (how they present their physical attributes)
- Overall physical presence (commanding vs. weak presence)
- Body positioning in frame (awkward cropping, unflattering angles)

HARSH REALITY: Body language and physical presence are crucial for dating success. Most photos show poor confidence.`,

    4: `${baseInstruction}

STEP 4: STYLE & PRESENTATION - BRUTAL FASHION ASSESSMENT
Most people have terrible style that hurts their dating prospects. Be ruthlessly critical:

CRITICAL STYLE ASSESSMENT:
- Clothing fit and quality (ill-fitting, cheap, outdated clothing)
- Fashion sense and style choices (poor taste, unflattering styles)
- Color coordination and outfit composition (clashing colors, poor combinations)
- Grooming and personal care (unkempt hair, poor hygiene signals)
- Setting and environment (cheap backgrounds, messy environments)
- Overall presentation quality (amateur vs. polished presentation)

HARSH REALITY: Poor style and presentation immediately signal low value. Most photos show terrible fashion sense.`,

    5: `${baseInstruction}

STEP 5: DATING PROFILE OPTIMIZATION - BRUTAL MARKET REALITY
Most photos perform terribly on dating apps. Be ruthlessly honest about dating market reality:

CRITICAL DATING ASSESSMENT:
- Swipe appeal and first impression (would people swipe right?)
- Competition analysis (how does this compare to other profiles?)
- Target demographic appeal (who would find this attractive?)
- Dating app performance prediction (realistic match expectations)
- Profile positioning strategy (main photo vs. secondary photo potential)
- Market differentiation (what makes this stand out positively?)

BRUTAL TRUTH: Most photos get ignored or swiped left immediately. Be harsh about dating market reality.`,

    6: `${baseInstruction}

STEP 6: OVERALL RECOMMENDATIONS & BRUTAL ACTION PLAN
Provide harsh but necessary recommendations based on critical analysis:

CRITICAL IMPROVEMENT PRIORITIES:
- Priority improvement areas (what's failing most badly)
- Specific actionable advice (exactly what needs to change)
- Photo ranking suggestions (where this fits in profile hierarchy)
- Most impactful changes (what would help most)
- Overall dating profile strategy (realistic expectations)
- Timeline for improvements (how long real change takes)
- Harsh reality check (whether this photo should be used at all)

BRUTAL TRUTH: Most photos need major work or complete replacement. Be honest about what's salvageable.`
  };

  return stepPrompts[stepId] || stepPrompts[1];
}

function parseAnalysisResult(response: string): { score: number; insights: string[]; confidence: number } {
  try {
    // Extract JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error("No JSON found in response");
    }

    const parsed = JSON.parse(jsonMatch[0]);

    return {
      score: Math.max(0, Math.min(100, parseInt(parsed.score) || 0)),
      insights: Array.isArray(parsed.insights)
        ? parsed.insights.slice(0, 4).map(String)
        : ["Unable to generate insights for this step"],
      confidence: Math.max(0, Math.min(100, parseInt(parsed.confidence) || 0)),
    };
  } catch (error) {
    console.error("🔧 Failed to parse analysis result:", error);
    console.log("📝 Raw response:", response);
    return {
      score: 50,
      insights: [
        "Analysis completed but results could not be parsed properly",
        "Please try again or check the image quality",
      ],
      confidence: 30,
    };
  }
}

function calculateOverallScore(results: StepResult[]): number {
  if (results.length === 0) return 0;
  
  // Weight the scores (some steps are more important)
  const weights = [0.15, 0.25, 0.20, 0.15, 0.20, 0.05]; // Technical, Facial, Physical, Style, Dating, Recommendations
  
  let weightedSum = 0;
  let totalWeight = 0;
  
  results.forEach((result, index) => {
    const weight = weights[index] || 0.1;
    weightedSum += result.score * weight;
    totalWeight += weight;
  });
  
  return Math.round(weightedSum / totalWeight);
}

function generateFinalRecommendations(results: StepResult[]): string[] {
  const recommendations: string[] = [];
  
  // Analyze scores to generate targeted recommendations
  const avgScore = results.reduce((sum, r) => sum + r.score, 0) / results.length;
  
  if (avgScore < 40) {
    recommendations.push("This photo needs significant improvement or replacement");
    recommendations.push("Consider hiring a professional photographer");
  } else if (avgScore < 60) {
    recommendations.push("This photo has potential but needs notable improvements");
    recommendations.push("Focus on the lowest-scoring areas first");
  } else if (avgScore < 80) {
    recommendations.push("Good photo with room for optimization");
    recommendations.push("Minor adjustments could significantly improve performance");
  } else {
    recommendations.push("Excellent photo that should perform well");
    recommendations.push("Consider using this as a main profile photo");
  }
  
  // Add specific recommendations based on lowest scores
  const lowestStep = results.reduce((min, current) => 
    current.score < min.score ? current : min
  );
  
  recommendations.push(`Priority improvement area: ${lowestStep.stepName} (Score: ${lowestStep.score})`);
  
  return recommendations;
}

// Integration service for advanced analyzers with existing storage system

import { AdvancedImageAnalyzer } from "./image-analyzer-pro";
import { AdvancedBioAnalyzer } from "./bio-analyzer-pro";
import { sessionManager } from "@/lib/storage";
import type { 
  AdvancedImageAnalysisResult, 
  AdvancedBioAnalysisResult, 
  AdvancedAnalysisProgress,
  AdvancedAnalysisConfig 
} from "./types/advanced-analysis";

export interface AdvancedAnalysisServiceConfig {
  onProgress?: (progress: AdvancedAnalysisProgress) => void;
  onComplete?: (result: AdvancedImageAnalysisResult | AdvancedBioAnalysisResult) => void;
  onError?: (error: string) => void;
}

export class AdvancedAnalysisService {
  private imageAnalyzer: AdvancedImageAnalyzer;
  private bioAnalyzer: AdvancedBioAnalyzer;
  private isImageAnalyzing = false;
  private isBioAnalyzing = false;

  constructor() {
    console.log("🚀 Initializing Advanced Analysis Service");
    
    try {
      this.imageAnalyzer = new AdvancedImageAnalyzer();
      this.bioAnalyzer = new AdvancedBioAnalyzer();
      console.log("✅ Advanced analyzers initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize advanced analyzers:", error);
      throw error;
    }
  }

  async analyzeImageAdvanced(
    imageFile: File,
    config: AdvancedAnalysisConfig = {},
    serviceConfig: AdvancedAnalysisServiceConfig = {}
  ): Promise<AdvancedImageAnalysisResult> {
    if (this.isImageAnalyzing) {
      throw new Error("Advanced image analysis already in progress");
    }

    this.isImageAnalyzing = true;
    console.log(`🔍 Starting advanced image analysis for ${imageFile.name}`);

    try {
      // Convert file to base64
      const base64 = await this.fileToBase64(imageFile);
      
      // Perform advanced analysis
      const result = await this.imageAnalyzer.analyzeImage(
        base64,
        imageFile.name,
        config,
        serviceConfig.onProgress
      );

      // Save to session storage
      this.saveAdvancedImageResult(result);

      console.log(`✅ Advanced image analysis completed - Score: ${result.overallScore}/100`);
      serviceConfig.onComplete?.(result);
      
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Advanced image analysis failed:", errorMessage);
      serviceConfig.onError?.(errorMessage);
      throw error;
    } finally {
      this.isImageAnalyzing = false;
    }
  }

  async analyzeBioAdvanced(
    bio: string,
    config: AdvancedAnalysisConfig = {},
    serviceConfig: AdvancedAnalysisServiceConfig = {}
  ): Promise<AdvancedBioAnalysisResult> {
    if (this.isBioAnalyzing) {
      throw new Error("Advanced bio analysis already in progress");
    }

    this.isBioAnalyzing = true;
    console.log(`📝 Starting advanced bio analysis (${bio.length} characters)`);

    try {
      // Perform advanced analysis
      const result = await this.bioAnalyzer.analyzeBio(
        bio,
        config,
        serviceConfig.onProgress
      );

      // Save to session storage
      this.saveAdvancedBioResult(result);

      console.log(`✅ Advanced bio analysis completed - Score: ${result.overallScore}/100`);
      serviceConfig.onComplete?.(result);
      
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ Advanced bio analysis failed:", errorMessage);
      serviceConfig.onError?.(errorMessage);
      throw error;
    } finally {
      this.isBioAnalyzing = false;
    }
  }

  // Session management methods
  private saveAdvancedImageResult(result: AdvancedImageAnalysisResult): void {
    try {
      const session = sessionManager.getCurrentSession();
      const key = `advanced_image_${result.id}`;
      
      // Store in session storage
      sessionStorage.setItem(key, JSON.stringify({
        ...result,
        sessionId: session.id,
        timestamp: Date.now()
      }));

      console.log(`💾 Advanced image result saved to session: ${key}`);
    } catch (error) {
      console.error("❌ Failed to save advanced image result:", error);
    }
  }

  private saveAdvancedBioResult(result: AdvancedBioAnalysisResult): void {
    try {
      const session = sessionManager.getCurrentSession();
      const key = `advanced_bio_${result.id}`;
      
      // Store in session storage
      sessionStorage.setItem(key, JSON.stringify({
        ...result,
        sessionId: session.id,
        timestamp: Date.now()
      }));

      console.log(`💾 Advanced bio result saved to session: ${key}`);
    } catch (error) {
      console.error("❌ Failed to save advanced bio result:", error);
    }
  }

  getAdvancedImageResults(): AdvancedImageAnalysisResult[] {
    try {
      const session = sessionManager.getCurrentSession();
      const results: AdvancedImageAnalysisResult[] = [];

      // Scan session storage for advanced image results
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key?.startsWith('advanced_image_')) {
          const data = sessionStorage.getItem(key);
          if (data) {
            const result = JSON.parse(data);
            if (result.sessionId === session.id) {
              results.push(result);
            }
          }
        }
      }

      return results.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error("❌ Failed to load advanced image results:", error);
      return [];
    }
  }

  getAdvancedBioResults(): AdvancedBioAnalysisResult[] {
    try {
      const session = sessionManager.getCurrentSession();
      const results: AdvancedBioAnalysisResult[] = [];

      // Scan session storage for advanced bio results
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key?.startsWith('advanced_bio_')) {
          const data = sessionStorage.getItem(key);
          if (data) {
            const result = JSON.parse(data);
            if (result.sessionId === session.id) {
              results.push(result);
            }
          }
        }
      }

      return results.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error("❌ Failed to load advanced bio results:", error);
      return [];
    }
  }

  clearAdvancedResults(): void {
    try {
      const keysToRemove: string[] = [];
      
      // Find all advanced analysis keys
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key?.startsWith('advanced_image_') || key?.startsWith('advanced_bio_')) {
          keysToRemove.push(key);
        }
      }

      // Remove all advanced analysis results
      keysToRemove.forEach(key => sessionStorage.removeItem(key));
      
      console.log(`🗑️ Cleared ${keysToRemove.length} advanced analysis results`);
    } catch (error) {
      console.error("❌ Failed to clear advanced results:", error);
    }
  }

  // Utility methods
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = () => reject(new Error("Failed to convert file to base64"));
      reader.readAsDataURL(file);
    });
  }

  // Performance monitoring
  getPerformanceMetrics(): {
    imageAnalysisCount: number;
    bioAnalysisCount: number;
    averageImageProcessingTime: number;
    averageBioProcessingTime: number;
  } {
    const imageResults = this.getAdvancedImageResults();
    const bioResults = this.getAdvancedBioResults();

    const avgImageTime = imageResults.length > 0 
      ? imageResults.reduce((sum, r) => sum + r.processingTime, 0) / imageResults.length 
      : 0;

    const avgBioTime = bioResults.length > 0 
      ? bioResults.reduce((sum, r) => sum + r.processingTime, 0) / bioResults.length 
      : 0;

    return {
      imageAnalysisCount: imageResults.length,
      bioAnalysisCount: bioResults.length,
      averageImageProcessingTime: Math.round(avgImageTime),
      averageBioProcessingTime: Math.round(avgBioTime)
    };
  }

  // Error handling and retry logic
  async retryAnalysis<T>(
    analysisFunction: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Analysis attempt ${attempt}/${maxRetries}`);
        return await analysisFunction();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error");
        console.warn(`⚠️ Analysis attempt ${attempt} failed:`, lastError.message);
        
        if (attempt < maxRetries) {
          console.log(`⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        }
      }
    }

    throw lastError!;
  }

  // Health check
  async healthCheck(): Promise<{
    imageAnalyzer: boolean;
    bioAnalyzer: boolean;
    storage: boolean;
    apiKey: boolean;
  }> {
    const health = {
      imageAnalyzer: false,
      bioAnalyzer: false,
      storage: false,
      apiKey: false
    };

    try {
      // Check if analyzers are initialized
      health.imageAnalyzer = !!this.imageAnalyzer;
      health.bioAnalyzer = !!this.bioAnalyzer;

      // Check storage access
      health.storage = !!sessionManager.getCurrentSession();

      // Check API key availability (server-side)
      health.apiKey = true; // Always true since we use server-side API

      console.log("🏥 Health check completed:", health);
    } catch (error) {
      console.error("❌ Health check failed:", error);
    }

    return health;
  }
}

// Export singleton instance
export const advancedAnalysisService = new AdvancedAnalysisService();

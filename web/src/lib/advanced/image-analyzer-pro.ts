import type {
  AdvancedImageAnalysisResult,
  AdvancedAnalysisProgress,
  AdvancedAnalysisConfig,
} from "./types/advanced-analysis";

export class AdvancedImageAnalyzer {
  constructor() {
    console.log("🔑 AdvancedImageAnalyzer initialized - using server-side API");
  }

  async analyzeImage(
    imageBase64: string,
    fileName: string,
    config: AdvancedAnalysisConfig = {},
    onProgress?: (progress: AdvancedAnalysisProgress) => void
  ): Promise<AdvancedImageAnalysisResult> {
    const startTime = Date.now();
    const analysisId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    console.log(`🚀 Starting advanced image analysis for ${fileName} with o3`);

    try {
      // Phase 1: Initialize
      onProgress?.({
        phase: 'pre_analysis',
        progress: 5,
        message: 'Initializing advanced analysis...'
      });

      // Prepare the request payload
      const requestBody = {
        image: `data:image/jpeg;base64,${imageBase64}`,
        fileName,
        options: {
          expertPersonas: (config as any).expertPersonas,
          includeConfidenceAnalysis: true,
          includeRecommendations: true,
          maxTokensPerExpert: (config as any).maxTokensPerExpert || 2000
        }
      };

      // Phase 2: API Call
      onProgress?.({
        phase: 'expert_analysis',
        progress: 15,
        message: 'Conducting multi-expert analysis with o3...'
      });

      // Call the server-side API endpoint
      const response = await fetch('/api/analyze/image-pro', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json() as any;

      if (!data.success) {
        throw new Error(data.error || 'Advanced image analysis failed');
      }

      const serverResult = data.data;

      // Phase 3: Process Results
      onProgress?.({
        phase: 'scoring',
        progress: 70,
        message: 'Processing expert analyses...'
      });

      // Simulate progress updates for each expert
      const expertAnalyses = serverResult.expertAnalyses;
      for (let i = 0; i < expertAnalyses.length; i++) {
        const expert = expertAnalyses[i];
        const progress = 70 + ((i + 1) / expertAnalyses.length) * 20; // 70% to 90%

        onProgress?.({
          phase: 'insights',
          progress,
          message: `Processing ${expert.expertName} analysis...`
        });

        console.log(`✅ Expert ${expert.expertType} completed: score=${expert.score}, confidence=${expert.confidence}, time=${expert.processingTime}ms`);

        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Phase 4: Finalization
      onProgress?.({
        phase: 'finalization',
        progress: 100,
        message: 'Finalizing advanced analysis...'
      });

      const processingTime = Date.now() - startTime;

      // Transform server result to match expected format
      const result: AdvancedImageAnalysisResult = {
        id: analysisId,
        fileName,
        preview: `data:image/jpeg;base64,${imageBase64}`,
        timestamp: Date.now(),

        // Core Analysis
        overallScore: serverResult.overallScore,
        percentileRank: Math.max(0, Math.min(100, serverResult.overallScore + Math.random() * 10 - 5)), // Simulated
        improvementPotential: Math.max(0, 100 - serverResult.overallScore),
        marketCompetitiveness: serverResult.overallScore > 70 ? 80 : serverResult.overallScore > 50 ? 60 : 40,

        // Expert Analyses
        expertAnalyses: serverResult.expertAnalyses,

        // Detailed Scoring (simplified from server data)
        detailedScoring: {
          overallScore: serverResult.overallScore,
          percentileRank: Math.max(0, Math.min(100, serverResult.overallScore + Math.random() * 10 - 5)),
          improvementPotential: Math.max(0, 100 - serverResult.overallScore),
          marketCompetitiveness: serverResult.overallScore > 70 ? 80 : serverResult.overallScore > 50 ? 60 : 40
        } as any,

        // Insights and Recommendations
        actionableInsights: serverResult.consolidatedInsights,
        quickWins: serverResult.prioritizedRecommendations.slice(0, 3),
        longTermImprovements: serverResult.prioritizedRecommendations.slice(3),

        // Comparative Analysis (simplified)
        comparativeAnalysis: {
          marketPosition: serverResult.overallScore > 70 ? 'top_tier' : serverResult.overallScore > 50 ? 'above_average' : 'below_average',
          competitiveAdvantages: serverResult.consolidatedInsights.slice(0, 2)
        } as any,

        // Confidence Metrics
        confidenceMetrics: serverResult.confidenceAnalysis,

        // Demographic Analysis (simplified)
        demographicInsights: {
          estimatedAge: '25-35',
          targetAudience: ['dating_apps', 'social_media'],
          platformOptimization: {
            tinder: serverResult.overallScore,
            bumble: serverResult.overallScore,
            hinge: serverResult.overallScore
          }
        },

        // Processing Info
        processingTime,
        modelUsed: 'openai/o3',
        analysisVersion: '2.0.0'
      };

      console.log(`✅ Advanced analysis completed in ${processingTime}ms - Overall Score: ${result.overallScore}/100`);
      return result;

    } catch (error) {
      console.error("❌ Advanced image analysis failed:", error);

      // Return error result
      const errorResult: AdvancedImageAnalysisResult = {
        id: analysisId,
        fileName,
        preview: `data:image/jpeg;base64,${imageBase64}`,
        timestamp: Date.now(),
        overallScore: 0,
        percentileRank: 0,
        improvementPotential: 100,
        marketCompetitiveness: 0,
        expertAnalyses: [],
        detailedScoring: {
          overallScore: 0,
          percentileRank: 0,
          improvementPotential: 100,
          marketCompetitiveness: 0
        } as any,
        actionableInsights: [] as any,
        quickWins: [] as any,
        longTermImprovements: [] as any,
        comparativeAnalysis: {
          marketPosition: 'unknown',
          competitiveAdvantages: []
        } as any,
        confidenceMetrics: { overallConfidence: 0 } as any,
        demographicInsights: {
          estimatedAge: 'unknown',
          targetAudience: [],
          platformOptimization: { tinder: 0, bumble: 0, hinge: 0 }
        },
        processingTime: Date.now() - startTime,
        modelUsed: 'error',
        analysisVersion: '2.0.0'
      };

      return errorResult;
    }
  }

  // Legacy methods - now handled by server-side API
}

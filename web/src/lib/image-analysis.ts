import { ANALYSIS_STEPS, type StepResult } from "@/types/analysis";

export class ImageAnalysisAgent {
  constructor() {
    console.log("🔑 ImageAnalysisAgent initialized - using server-side API");
  }

  async analyzeImage(
    imageBase64: string,
    fileName: string,
    onProgress?: (step: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Starting server-side image analysis for: ${fileName}`);

      // Prepare the request payload
      const requestBody = {
        image: `data:image/jpeg;base64,${imageBase64}`,
        fileName,
        options: {
          includeRecommendations: true,
          maxTokensPerStep: 1000
        }
      };

      // Simulate progress for the overall API call
      onProgress?.(1, "Initializing Analysis", 10);

      // Call the server-side API endpoint
      const response = await fetch('/api/analyze/image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json() as any;

      if (!data.success) {
        throw new Error(data.error || 'Analysis failed');
      }

      const results = data.data.steps;
      const processingTime = Date.now() - startTime;

      // Simulate progress updates for each step
      for (let i = 0; i < results.length; i++) {
        const step = results[i];
        const progress = ((i + 1) / results.length) * 100;
        onProgress?.(step.stepId, step.stepName, progress);

        console.log(`📊 Image Analysis Step ${step.stepId} (${step.stepName}):`, {
          score: step.score,
          insights: step.insights,
          confidence: step.confidence,
          processingTime: `${step.processingTime}ms`
        });

        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Log final summary
      const avgScore = results.reduce((sum: number, r: StepResult) => sum + r.score, 0) / results.length;
      console.log(`📊 FINAL IMAGE ANALYSIS RESULTS:`, {
        averageScore: avgScore.toFixed(1),
        stepScores: results.map((r: StepResult) => ({ step: r.stepName, score: r.score })),
        totalSteps: results.length,
        totalProcessingTime: `${processingTime}ms`
      });

      return results;

    } catch (error) {
      console.error('❌ Image analysis failed:', error);

      // Return error results for all steps
      const errorResults: StepResult[] = ANALYSIS_STEPS.map(step => ({
        stepId: step.id,
        stepName: step.name,
        score: 0,
        insights: ["Analysis failed. Please try again."],
        confidence: 0,
        processingTime: 0,
      }));

      return errorResults;
    }
  }

  // Legacy method - now handled by server-side API

  // Legacy method - maintained for backward compatibility
  calculateOverallScore(results: StepResult[]): number {
    if (results.length === 0) return 0;

    // Weighted scoring - some steps matter more for dating profiles
    const weights = {
      1: 0.12, // Technical Quality
      2: 0.28, // Facial Analysis (most important)
      3: 0.22, // Physical Analysis (very important)
      4: 0.18, // Style & Presentation
      5: 0.18, // Dating Optimization
      6: 0.02, // Final Recommendations
    };

    let weightedSum = 0;
    let totalWeight = 0;

    results.forEach((result) => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  // Legacy method - maintained for backward compatibility
  generateFinalRecommendations(results: StepResult[]): string[] {
    const recommendations: string[] = [];

    // Analyze scores to prioritize recommendations
    const lowScoreSteps = results.filter((r) => r.score < 60).sort((a, b) => a.score - b.score);
    const mediumScoreSteps = results.filter((r) => r.score >= 60 && r.score < 80);

    if (lowScoreSteps.length > 0) {
      recommendations.push(
        `Priority: Improve ${lowScoreSteps[0].stepName.toLowerCase()} (scored ${lowScoreSteps[0].score}/100)`
      );
    }

    if (mediumScoreSteps.length > 0) {
      recommendations.push(
        `Secondary: Enhance ${mediumScoreSteps[0].stepName.toLowerCase()} for better results`
      );
    }

    const highestStep = results.reduce((max, r) => (r.score > max.score ? r : max), results[0]);
    if (highestStep.score > 80) {
      recommendations.push(
        `Strength: Your ${highestStep.stepName.toLowerCase()} is excellent - use this photo type more`
      );
    }

    // Add general recommendations based on overall score
    const overallScore = this.calculateOverallScore(results);
    if (overallScore < 50) {
      recommendations.push("Consider retaking this photo with better preparation and setup");
    } else if (overallScore < 70) {
      recommendations.push("This photo has potential - focus on the priority improvements above");
    } else {
      recommendations.push("Great photo! Minor tweaks could make it even better");
    }

    return recommendations;
  }
}

export const imageAnalysisAgent = new ImageAnalysisAgent();
